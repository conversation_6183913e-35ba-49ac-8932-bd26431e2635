[workspace]
members = [
    "backend",
    "shared"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["SecureFox Team"]
license = "MIT"
repository = "https://github.com/securefox/securefox"

[workspace.dependencies]
# Web framework
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }

# Crypto
ring = "0.17"
argon2 = "0.5"

# Authentication
jsonwebtoken = "9.0"

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
anyhow = "1.0"
thiserror = "1.0"

# Logging
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Configuration
config = "0.14"
